import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../services/auth_service.dart';
import '../screens/login_screen.dart';

class SideMenu extends StatelessWidget {
  final String currentRoute;
  final Function(String) onMenuItemSelected;

  const SideMenu({
    super.key,
    required this.currentRoute,
    required this.onMenuItemSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthService>(
      builder: (context, authService, child) {
        return Drawer(
          child: Column(
            children: [
              // Header
              Container(
                height: 200,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.blue.shade600,
                      Colors.purple.shade600,
                    ],
                  ),
                ),
                child: DrawerHeader(
                  margin: EdgeInsets.zero,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: Colors.white,
                        child: Icon(
                          authService.isAdmin ? Icons.admin_panel_settings : Icons.person,
                          size: 30,
                          color: Colors.blue.shade600,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        authService.currentUser?.username ?? 'User',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        authService.currentUser?.role.toUpperCase() ?? '',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              
              // Menu Items
              Expanded(
                child: ListView(
                  padding: EdgeInsets.zero,
                  children: [
                    // Home
                    _buildMenuItem(
                      context,
                      icon: Icons.home,
                      title: 'Home',
                      route: 'home',
                      isSelected: currentRoute == 'home',
                    ),
                    
                    // About
                    _buildMenuItem(
                      context,
                      icon: Icons.info,
                      title: 'About',
                      route: 'about',
                      isSelected: currentRoute == 'about',
                    ),
                    
                    // Admin-only menu items
                    if (authService.isAdmin) ...[
                      const Divider(),
                      _buildSectionHeader('Admin'),
                      _buildMenuItem(
                        context,
                        icon: Icons.people,
                        title: 'Add User',
                        route: 'user_management',
                        isSelected: currentRoute == 'user_management',
                      ),
                      _buildMenuItem(
                        context,
                        icon: Icons.settings,
                        title: 'Settings',
                        route: 'settings',
                        isSelected: currentRoute == 'settings',
                      ),
                    ],
                    
                    // Client-only menu items
                    if (authService.isClient) ...[
                      const Divider(),
                      _buildSectionHeader('Student Management'),
                      _buildMenuItem(
                        context,
                        icon: Icons.school,
                        title: 'Students',
                        route: 'students',
                        isSelected: currentRoute == 'students',
                      ),
                    ],
                  ],
                ),
              ),
              
              // Logout
              const Divider(),
              ListTile(
                leading: const Icon(Icons.logout, color: Colors.red),
                title: const Text(
                  'Logout',
                  style: TextStyle(color: Colors.red),
                ),
                onTap: () => _logout(context, authService),
              ),
              const SizedBox(height: 16),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.grey.shade600,
          letterSpacing: 1.2,
        ),
      ),
    );
  }

  Widget _buildMenuItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: isSelected ? Colors.blue.shade50 : null,
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.blue.shade600 : Colors.grey.shade600,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.blue.shade600 : Colors.grey.shade800,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        selected: isSelected,
        onTap: () {
          Navigator.of(context).pop(); // Close drawer
          onMenuItemSelected(route);
        },
      ),
    );
  }

  Future<void> _logout(BuildContext context, AuthService authService) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Logout'),
          ),
        ],
      ),
    );

    if (confirmed == true && context.mounted) {
      await authService.logout();
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );
    }
  }
}
