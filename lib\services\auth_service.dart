import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../database/database_helper.dart';
import '../models/user.dart';

class AuthService extends ChangeNotifier {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  User? _currentUser;
  bool _isLoggedIn = false;

  User? get currentUser => _currentUser;
  bool get isLoggedIn => _isLoggedIn;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  bool get isClient => _currentUser?.isClient ?? false;

  Future<void> initialize() async {
    await _loadUserFromPreferences();
  }

  Future<bool> login(String username, String password) async {
    try {
      // Check if app is expired and user is not admin
      final isExpired = await _dbHelper.isAppExpired();
      if (isExpired && username != 'admin') {
        throw Exception('App has expired. Only admin can login.');
      }

      final user = await _dbHelper.authenticateUser(username, password);
      if (user != null) {
        _currentUser = user;
        _isLoggedIn = true;
        await _saveUserToPreferences();
        notifyListeners();
        return true;
      }
      return false;
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    _currentUser = null;
    _isLoggedIn = false;
    await _clearUserFromPreferences();
    notifyListeners();
  }

  Future<void> _saveUserToPreferences() async {
    if (_currentUser != null) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('user_id', _currentUser!.id.toString());
      await prefs.setString('username', _currentUser!.username);
      await prefs.setString('role', _currentUser!.role);
      await prefs.setBool('is_logged_in', true);
    }
  }

  Future<void> _loadUserFromPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    final isLoggedIn = prefs.getBool('is_logged_in') ?? false;
    
    if (isLoggedIn) {
      final username = prefs.getString('username');
      if (username != null) {
        // Verify user still exists and is active
        final users = await _dbHelper.getAllUsers();
        final user = users.where((u) => u.username == username && u.isActive).firstOrNull;
        
        if (user != null) {
          _currentUser = user;
          _isLoggedIn = true;
          notifyListeners();
        } else {
          await _clearUserFromPreferences();
        }
      }
    }
  }

  Future<void> _clearUserFromPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_id');
    await prefs.remove('username');
    await prefs.remove('role');
    await prefs.setBool('is_logged_in', false);
  }

  Future<bool> createUser(String username, String password, String role) async {
    try {
      if (!isAdmin) {
        throw Exception('Only admin can create users');
      }

      final user = User(
        username: username,
        password: password,
        role: role,
      );

      await _dbHelper.insertUser(user);
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<List<User>> getAllUsers() async {
    if (!isAdmin) {
      throw Exception('Only admin can view all users');
    }
    return await _dbHelper.getAllUsers();
  }

  Future<bool> resetUserPassword(int userId, String newPassword) async {
    try {
      if (!isAdmin) {
        throw Exception('Only admin can reset passwords');
      }

      await _dbHelper.resetUserPassword(userId, newPassword);
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> deleteUser(int userId) async {
    try {
      if (!isAdmin) {
        throw Exception('Only admin can delete users');
      }

      // Don't allow deleting the current user
      if (_currentUser?.id == userId) {
        throw Exception('Cannot delete current user');
      }

      await _dbHelper.deleteUser(userId);
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<bool> updateAppExpiry(DateTime newExpiryDate) async {
    try {
      if (!isAdmin) {
        throw Exception('Only admin can update app expiry');
      }

      await _dbHelper.updateSetting(
        'app_expiry_date',
        newExpiryDate.toIso8601String(),
      );
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future<DateTime?> getAppExpiryDate() async {
    return await _dbHelper.getAppExpiryDate();
  }

  Future<bool> isAppExpired() async {
    return await _dbHelper.isAppExpired();
  }
}
