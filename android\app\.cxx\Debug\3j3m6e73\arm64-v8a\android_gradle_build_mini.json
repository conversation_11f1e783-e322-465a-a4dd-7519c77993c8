{"buildFiles": ["E:\\Dev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\flutter\\student_demo\\android\\app\\.cxx\\Debug\\3j3m6e73\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\flutter\\student_demo\\android\\app\\.cxx\\Debug\\3j3m6e73\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}