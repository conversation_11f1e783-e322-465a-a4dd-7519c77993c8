import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../utils/constants.dart';

class NetworkService extends ChangeNotifier {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();

  bool _isConnected = true; // Simplified - assume always connected
  String _connectionStatus = 'Connected';

  bool get isConnected => _isConnected;
  String get connectionStatusText => _connectionStatus;

  Future<void> initialize() async {
    // Simplified initialization
    await _updateLastNetworkCheck();
  }

  Future<void> _updateConnectionStatus() async {
    // Simplified - always connected
    _isConnected = true;
    _connectionStatus = 'Connected';
    await _updateLastNetworkCheck();
    notifyListeners();
  }

  Future<void> _updateLastNetworkCheck() async {
    try {
      await _dbHelper.updateSetting(
        SettingsKeys.lastNetworkCheck,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // Ignore errors when updating network check time
    }
  }

  Future<DateTime?> getLastNetworkCheck() async {
    try {
      final setting = await _dbHelper.getSetting(SettingsKeys.lastNetworkCheck);
      if (setting != null) {
        return DateTime.parse(setting.value);
      }
    } catch (e) {
      // Return null if there's an error
    }
    return null;
  }

  Future<bool> checkConnectivity() async {
    await _updateConnectionStatus();
    return _isConnected;
  }

  Future<Map<String, dynamic>> getNetworkInfo() async {
    final lastCheck = await getLastNetworkCheck();

    return {
      'status': connectionStatusText,
      'isConnected': _isConnected,
      'connectionType': 'wifi',
      'lastCheck': lastCheck?.toIso8601String(),
      'lastCheckFormatted':
          lastCheck != null
              ? '${lastCheck.day}/${lastCheck.month}/${lastCheck.year} ${lastCheck.hour}:${lastCheck.minute.toString().padLeft(2, '0')}'
              : 'Never',
    };
  }
}
