import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../database/database_helper.dart';
import '../utils/constants.dart';

class NetworkService extends ChangeNotifier {
  static final NetworkService _instance = NetworkService._internal();
  factory NetworkService() => _instance;
  NetworkService._internal();

  final Connectivity _connectivity = Connectivity();
  final DatabaseHelper _dbHelper = DatabaseHelper();
  
  ConnectivityResult _connectionStatus = ConnectivityResult.none;
  bool _isConnected = false;

  ConnectivityResult get connectionStatus => _connectionStatus;
  bool get isConnected => _isConnected;

  String get connectionStatusText {
    switch (_connectionStatus) {
      case ConnectivityResult.wifi:
        return 'Connected to WiFi';
      case ConnectivityResult.mobile:
        return 'Connected to Mobile Data';
      case ConnectivityResult.ethernet:
        return 'Connected to Ethernet';
      case ConnectivityResult.vpn:
        return 'Connected via VPN';
      case ConnectivityResult.bluetooth:
        return 'Connected via Bluetooth';
      case ConnectivityResult.other:
        return 'Connected to Other Network';
      case ConnectivityResult.none:
      default:
        return 'No Internet Connection';
    }
  }

  Future<void> initialize() async {
    // Check initial connectivity status
    await _updateConnectionStatus();
    
    // Listen for connectivity changes
    _connectivity.onConnectivityChanged.listen((ConnectivityResult result) {
      _updateConnectionStatus();
    });
  }

  Future<void> _updateConnectionStatus() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _connectionStatus = result;
      _isConnected = result != ConnectivityResult.none;
      
      // Update last network check time
      await _updateLastNetworkCheck();
      
      notifyListeners();
    } catch (e) {
      _connectionStatus = ConnectivityResult.none;
      _isConnected = false;
      notifyListeners();
    }
  }

  Future<void> _updateLastNetworkCheck() async {
    try {
      await _dbHelper.updateSetting(
        SettingsKeys.lastNetworkCheck,
        DateTime.now().toIso8601String(),
      );
    } catch (e) {
      // Ignore errors when updating network check time
    }
  }

  Future<DateTime?> getLastNetworkCheck() async {
    try {
      final setting = await _dbHelper.getSetting(SettingsKeys.lastNetworkCheck);
      if (setting != null) {
        return DateTime.parse(setting.value);
      }
    } catch (e) {
      // Return null if there's an error
    }
    return null;
  }

  Future<bool> checkConnectivity() async {
    await _updateConnectionStatus();
    return _isConnected;
  }

  Future<Map<String, dynamic>> getNetworkInfo() async {
    final lastCheck = await getLastNetworkCheck();
    
    return {
      'status': connectionStatusText,
      'isConnected': _isConnected,
      'connectionType': _connectionStatus.toString(),
      'lastCheck': lastCheck?.toIso8601String(),
      'lastCheckFormatted': lastCheck != null 
          ? '${lastCheck.day}/${lastCheck.month}/${lastCheck.year} ${lastCheck.hour}:${lastCheck.minute.toString().padLeft(2, '0')}'
          : 'Never',
    };
  }
}
