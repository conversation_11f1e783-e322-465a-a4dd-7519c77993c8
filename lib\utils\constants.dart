class AppConstants {
  // Database
  static const String dbName = 'student_demo.db';
  static const String dbPassword = 'StudentDemo@2024';
  static const int dbVersion = 1;
  
  // Default Admin Credentials
  static const String defaultAdminId = 'admin';
  static const String defaultAdminPassword = 'Admin@789';
  
  // App Settings
  static const String appName = 'Demo App';
  static const String companyName = 'Dummy Company Ltd.';
  static const String companyInfo = '''
Dummy Company Ltd. is a leading technology company 
specializing in educational software solutions. 
Founded in 2020, we have been serving educational 
institutions with innovative digital tools.

Contact Information:
Email: <EMAIL>
Phone: ******-567-8900
Address: 123 Tech Street, Digital City, DC 12345
  ''';
  
  // User Roles
  static const String adminRole = 'admin';
  static const String clientRole = 'client';
  
  // Pagination
  static const int defaultPageSize = 10;
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String displayDateFormat = 'dd/MM/yyyy';
  
  // Validation
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String phonePattern = r'^\+?[\d\s\-\(\)]{10,}$';
}

class DatabaseTables {
  static const String users = 'users';
  static const String students = 'students';
  static const String appSettings = 'app_settings';
}

class UserFields {
  static const String id = 'id';
  static const String username = 'username';
  static const String password = 'password';
  static const String role = 'role';
  static const String isActive = 'is_active';
  static const String createdAt = 'created_at';
  static const String updatedAt = 'updated_at';
}

class StudentFields {
  static const String id = 'id';
  static const String name = 'name';
  static const String mobile = 'mobile';
  static const String email = 'email';
  static const String dateOfBirth = 'date_of_birth';
  static const String photo = 'photo';
  static const String createdBy = 'created_by';
  static const String createdAt = 'created_at';
  static const String updatedAt = 'updated_at';
}

class AppSettingsFields {
  static const String id = 'id';
  static const String key = 'key';
  static const String value = 'value';
  static const String updatedAt = 'updated_at';
}

class SettingsKeys {
  static const String appExpiryDate = 'app_expiry_date';
  static const String lastNetworkCheck = 'last_network_check';
}
