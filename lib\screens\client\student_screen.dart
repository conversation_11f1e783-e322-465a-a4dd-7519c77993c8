import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/auth_service.dart';
import '../../database/database_helper.dart';
import '../../models/student.dart';
import '../../utils/constants.dart';
import '../../widgets/student_form_dialog.dart';

class StudentScreen extends StatefulWidget {
  const StudentScreen({super.key});

  @override
  State<StudentScreen> createState() => _StudentScreenState();
}

class _StudentScreenState extends State<StudentScreen> {
  final DatabaseHelper _dbHelper = DatabaseHelper();
  final TextEditingController _searchController = TextEditingController();
  
  List<Student> _students = [];
  bool _isLoading = true;
  int _currentPage = 0;
  int _totalStudents = 0;
  String _searchQuery = '';
  
  static const int _pageSize = AppConstants.defaultPageSize;

  @override
  void initState() {
    super.initState();
    _loadStudents();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadStudents() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final students = await _dbHelper.getAllStudents(
        limit: _pageSize,
        offset: _currentPage * _pageSize,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );
      
      final totalCount = await _dbHelper.getStudentsCount(
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      setState(() {
        _students = students;
        _totalStudents = totalCount;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('Failed to load students: $e');
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _currentPage = 0;
    });
    _loadStudents();
  }

  void _nextPage() {
    if ((_currentPage + 1) * _pageSize < _totalStudents) {
      setState(() {
        _currentPage++;
      });
      _loadStudents();
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
      });
      _loadStudents();
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _showAddStudentDialog() async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final result = await showDialog<Student>(
      context: context,
      builder: (context) => StudentFormDialog(
        currentUser: authService.currentUser!.username,
      ),
    );

    if (result != null) {
      try {
        await _dbHelper.insertStudent(result);
        _showSuccessSnackBar('Student added successfully');
        _loadStudents();
      } catch (e) {
        _showErrorDialog('Failed to add student: $e');
      }
    }
  }

  Future<void> _showEditStudentDialog(Student student) async {
    final authService = Provider.of<AuthService>(context, listen: false);
    final result = await showDialog<Student>(
      context: context,
      builder: (context) => StudentFormDialog(
        student: student,
        currentUser: authService.currentUser!.username,
      ),
    );

    if (result != null) {
      try {
        await _dbHelper.updateStudent(result);
        _showSuccessSnackBar('Student updated successfully');
        _loadStudents();
      } catch (e) {
        _showErrorDialog('Failed to update student: $e');
      }
    }
  }

  Future<void> _deleteStudent(Student student) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Student'),
        content: Text('Are you sure you want to delete "${student.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await _dbHelper.deleteStudent(student.id!);
        _showSuccessSnackBar('Student deleted successfully');
        _loadStudents();
      } catch (e) {
        _showErrorDialog('Failed to delete student: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalPages = (_totalStudents / _pageSize).ceil();

    return Scaffold(
      body: Column(
        children: [
        // Header with Search
        Container(
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    'Students',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    'Total: $_totalStudents',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Search Bar
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search by name, mobile, or email...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _onSearchChanged('');
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onChanged: _onSearchChanged,
              ),
            ],
          ),
        ),
        
        // Students List
        Expanded(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _students.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.school_outlined,
                            size: 64,
                            color: Colors.grey.shade400,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchQuery.isEmpty
                                ? 'No students found'
                                : 'No students match your search',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 24),
                      itemCount: _students.length,
                      itemBuilder: (context, index) {
                        final student = _students[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: 12),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: Colors.blue.shade100,
                              child: student.photo != null
                                  ? ClipOval(
                                      child: Image.network(
                                        student.photo!,
                                        width: 40,
                                        height: 40,
                                        fit: BoxFit.cover,
                                        errorBuilder: (context, error, stackTrace) =>
                                            Icon(Icons.person, color: Colors.blue.shade600),
                                      ),
                                    )
                                  : Icon(Icons.person, color: Colors.blue.shade600),
                            ),
                            title: Text(
                              student.name,
                              style: const TextStyle(fontWeight: FontWeight.w600),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Mobile: ${student.mobile}'),
                                Text('Email: ${student.email}'),
                                Text('DOB: ${DateFormat(AppConstants.displayDateFormat).format(student.dateOfBirth)}'),
                                Text('Age: ${student.age} years'),
                              ],
                            ),
                            trailing: PopupMenuButton<String>(
                              onSelected: (value) {
                                switch (value) {
                                  case 'edit':
                                    _showEditStudentDialog(student);
                                    break;
                                  case 'delete':
                                    _deleteStudent(student);
                                    break;
                                }
                              },
                              itemBuilder: (context) => [
                                const PopupMenuItem(
                                  value: 'edit',
                                  child: Row(
                                    children: [
                                      Icon(Icons.edit),
                                      SizedBox(width: 8),
                                      Text('Edit'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'delete',
                                  child: Row(
                                    children: [
                                      Icon(Icons.delete, color: Colors.red),
                                      SizedBox(width: 8),
                                      Text('Delete', style: TextStyle(color: Colors.red)),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
        ),
        
        // Pagination
        if (_totalStudents > _pageSize)
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  onPressed: _currentPage > 0 ? _previousPage : null,
                  child: const Text('Previous'),
                ),
                Text(
                  'Page ${_currentPage + 1} of $totalPages',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                ElevatedButton(
                  onPressed: (_currentPage + 1) * _pageSize < _totalStudents ? _nextPage : null,
                  child: const Text('Next'),
                ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddStudentDialog,
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }
}
