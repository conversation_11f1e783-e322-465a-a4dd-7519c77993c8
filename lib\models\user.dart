import '../utils/constants.dart';

class User {
  final int? id;
  final String username;
  final String password;
  final String role;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  User({
    this.id,
    required this.username,
    required this.password,
    required this.role,
    this.isActive = true,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      UserFields.id: id,
      UserFields.username: username,
      UserFields.password: password,
      UserFields.role: role,
      UserFields.isActive: isActive ? 1 : 0,
      UserFields.createdAt: createdAt.toIso8601String(),
      UserFields.updatedAt: updatedAt.toIso8601String(),
    };
  }

  factory User.fromMap(Map<String, dynamic> map) {
    return User(
      id: map[UserFields.id]?.toInt(),
      username: map[UserFields.username] ?? '',
      password: map[UserFields.password] ?? '',
      role: map[UserFields.role] ?? '',
      isActive: (map[UserFields.isActive] ?? 1) == 1,
      createdAt: DateTime.parse(map[UserFields.createdAt]),
      updatedAt: DateTime.parse(map[UserFields.updatedAt]),
    );
  }

  User copyWith({
    int? id,
    String? username,
    String? password,
    String? role,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      password: password ?? this.password,
      role: role ?? this.role,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isAdmin => role == AppConstants.adminRole;
  bool get isClient => role == AppConstants.clientRole;

  @override
  String toString() {
    return 'User{id: $id, username: $username, role: $role, isActive: $isActive}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is User &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          username == other.username;

  @override
  int get hashCode => id.hashCode ^ username.hashCode;
}
