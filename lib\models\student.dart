import '../utils/constants.dart';

class Student {
  final int? id;
  final String name;
  final String mobile;
  final String email;
  final DateTime dateOfBirth;
  final String? photo;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  Student({
    this.id,
    required this.name,
    required this.mobile,
    required this.email,
    required this.dateOfBirth,
    this.photo,
    required this.createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      StudentFields.id: id,
      StudentFields.name: name,
      StudentFields.mobile: mobile,
      StudentFields.email: email,
      StudentFields.dateOfBirth: dateOfBirth.toIso8601String(),
      StudentFields.photo: photo,
      StudentFields.createdBy: createdBy,
      StudentFields.createdAt: createdAt.toIso8601String(),
      StudentFields.updatedAt: updatedAt.toIso8601String(),
    };
  }

  factory Student.fromMap(Map<String, dynamic> map) {
    return Student(
      id: map[StudentFields.id]?.toInt(),
      name: map[StudentFields.name] ?? '',
      mobile: map[StudentFields.mobile] ?? '',
      email: map[StudentFields.email] ?? '',
      dateOfBirth: DateTime.parse(map[StudentFields.dateOfBirth]),
      photo: map[StudentFields.photo],
      createdBy: map[StudentFields.createdBy] ?? '',
      createdAt: DateTime.parse(map[StudentFields.createdAt]),
      updatedAt: DateTime.parse(map[StudentFields.updatedAt]),
    );
  }

  Student copyWith({
    int? id,
    String? name,
    String? mobile,
    String? email,
    DateTime? dateOfBirth,
    String? photo,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Student(
      id: id ?? this.id,
      name: name ?? this.name,
      mobile: mobile ?? this.mobile,
      email: email ?? this.email,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      photo: photo ?? this.photo,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  int get age {
    final now = DateTime.now();
    int age = now.year - dateOfBirth.year;
    if (now.month < dateOfBirth.month ||
        (now.month == dateOfBirth.month && now.day < dateOfBirth.day)) {
      age--;
    }
    return age;
  }

  @override
  String toString() {
    return 'Student{id: $id, name: $name, mobile: $mobile, email: $email}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Student &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          email == other.email;

  @override
  int get hashCode => id.hashCode ^ email.hashCode;
}
