import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';

import '../models/user.dart';
import '../models/student.dart';
import '../models/app_settings.dart';
import '../utils/constants.dart';

class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, AppConstants.dbName);

    return await openDatabase(
      path,
      version: AppConstants.dbVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Create users table
    await db.execute('''
      CREATE TABLE ${DatabaseTables.users} (
        ${UserFields.id} INTEGER PRIMARY KEY AUTOINCREMENT,
        ${UserFields.username} TEXT UNIQUE NOT NULL,
        ${UserFields.password} TEXT NOT NULL,
        ${UserFields.role} TEXT NOT NULL,
        ${UserFields.isActive} INTEGER NOT NULL DEFAULT 1,
        ${UserFields.createdAt} TEXT NOT NULL,
        ${UserFields.updatedAt} TEXT NOT NULL
      )
    ''');

    // Create students table
    await db.execute('''
      CREATE TABLE ${DatabaseTables.students} (
        ${StudentFields.id} INTEGER PRIMARY KEY AUTOINCREMENT,
        ${StudentFields.name} TEXT NOT NULL,
        ${StudentFields.mobile} TEXT NOT NULL,
        ${StudentFields.email} TEXT UNIQUE NOT NULL,
        ${StudentFields.dateOfBirth} TEXT NOT NULL,
        ${StudentFields.photo} TEXT,
        ${StudentFields.createdBy} TEXT NOT NULL,
        ${StudentFields.createdAt} TEXT NOT NULL,
        ${StudentFields.updatedAt} TEXT NOT NULL
      )
    ''');

    // Create app_settings table
    await db.execute('''
      CREATE TABLE ${DatabaseTables.appSettings} (
        ${AppSettingsFields.id} INTEGER PRIMARY KEY AUTOINCREMENT,
        ${AppSettingsFields.key} TEXT UNIQUE NOT NULL,
        ${AppSettingsFields.value} TEXT NOT NULL,
        ${AppSettingsFields.updatedAt} TEXT NOT NULL
      )
    ''');

    // Insert default admin user
    await _insertDefaultAdmin(db);
    
    // Insert default app settings
    await _insertDefaultSettings(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    // Handle database upgrades here
  }

  Future<void> _insertDefaultAdmin(Database db) async {
    final hashedPassword = _hashPassword(AppConstants.defaultAdminPassword);
    final admin = User(
      username: AppConstants.defaultAdminId,
      password: hashedPassword,
      role: AppConstants.adminRole,
    );
    
    await db.insert(DatabaseTables.users, admin.toMap());
  }

  Future<void> _insertDefaultSettings(Database db) async {
    // Set default app expiry to 1 year from now
    final expiryDate = DateTime.now().add(const Duration(days: 365));
    
    final settings = [
      AppSettings(
        key: SettingsKeys.appExpiryDate,
        value: expiryDate.toIso8601String(),
      ),
      AppSettings(
        key: SettingsKeys.lastNetworkCheck,
        value: DateTime.now().toIso8601String(),
      ),
    ];
    
    for (final setting in settings) {
      await db.insert(DatabaseTables.appSettings, setting.toMap());
    }
  }

  String _hashPassword(String password) {
    var bytes = utf8.encode(password);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  // User operations
  Future<User?> authenticateUser(String username, String password) async {
    final db = await database;
    final hashedPassword = _hashPassword(password);
    
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseTables.users,
      where: '${UserFields.username} = ? AND ${UserFields.password} = ? AND ${UserFields.isActive} = 1',
      whereArgs: [username, hashedPassword],
    );
    
    if (maps.isNotEmpty) {
      return User.fromMap(maps.first);
    }
    return null;
  }

  Future<int> insertUser(User user) async {
    final db = await database;
    final userWithHashedPassword = user.copyWith(
      password: _hashPassword(user.password),
    );
    return await db.insert(DatabaseTables.users, userWithHashedPassword.toMap());
  }

  Future<List<User>> getAllUsers() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseTables.users,
      orderBy: '${UserFields.createdAt} DESC',
    );
    
    return List.generate(maps.length, (i) => User.fromMap(maps[i]));
  }

  Future<int> updateUser(User user) async {
    final db = await database;
    return await db.update(
      DatabaseTables.users,
      user.toMap(),
      where: '${UserFields.id} = ?',
      whereArgs: [user.id],
    );
  }

  Future<int> resetUserPassword(int userId, String newPassword) async {
    final db = await database;
    final hashedPassword = _hashPassword(newPassword);
    return await db.update(
      DatabaseTables.users,
      {
        UserFields.password: hashedPassword,
        UserFields.updatedAt: DateTime.now().toIso8601String(),
      },
      where: '${UserFields.id} = ?',
      whereArgs: [userId],
    );
  }

  Future<int> deleteUser(int id) async {
    final db = await database;
    return await db.delete(
      DatabaseTables.users,
      where: '${UserFields.id} = ?',
      whereArgs: [id],
    );
  }

  // Student operations
  Future<int> insertStudent(Student student) async {
    final db = await database;
    return await db.insert(DatabaseTables.students, student.toMap());
  }

  Future<List<Student>> getAllStudents({
    int? limit,
    int? offset,
    String? searchQuery,
  }) async {
    final db = await database;
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause = '''
        ${StudentFields.name} LIKE ? OR
        ${StudentFields.mobile} LIKE ? OR
        ${StudentFields.email} LIKE ?
      ''';
      final searchPattern = '%$searchQuery%';
      whereArgs = [searchPattern, searchPattern, searchPattern];
    }

    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseTables.students,
      where: whereClause.isEmpty ? null : whereClause,
      whereArgs: whereArgs.isEmpty ? null : whereArgs,
      orderBy: '${StudentFields.createdAt} DESC',
      limit: limit,
      offset: offset,
    );

    return List.generate(maps.length, (i) => Student.fromMap(maps[i]));
  }

  Future<int> getStudentsCount({String? searchQuery}) async {
    final db = await database;
    String whereClause = '';
    List<dynamic> whereArgs = [];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause = '''
        ${StudentFields.name} LIKE ? OR
        ${StudentFields.mobile} LIKE ? OR
        ${StudentFields.email} LIKE ?
      ''';
      final searchPattern = '%$searchQuery%';
      whereArgs = [searchPattern, searchPattern, searchPattern];
    }

    final result = await db.query(
      DatabaseTables.students,
      columns: ['COUNT(*) as count'],
      where: whereClause.isEmpty ? null : whereClause,
      whereArgs: whereArgs.isEmpty ? null : whereArgs,
    );

    return result.first['count'] as int;
  }

  Future<Student?> getStudentById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseTables.students,
      where: '${StudentFields.id} = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return Student.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateStudent(Student student) async {
    final db = await database;
    return await db.update(
      DatabaseTables.students,
      student.toMap(),
      where: '${StudentFields.id} = ?',
      whereArgs: [student.id],
    );
  }

  Future<int> deleteStudent(int id) async {
    final db = await database;
    return await db.delete(
      DatabaseTables.students,
      where: '${StudentFields.id} = ?',
      whereArgs: [id],
    );
  }

  // App Settings operations
  Future<AppSettings?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      DatabaseTables.appSettings,
      where: '${AppSettingsFields.key} = ?',
      whereArgs: [key],
    );

    if (maps.isNotEmpty) {
      return AppSettings.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateSetting(String key, String value) async {
    final db = await database;
    final setting = AppSettings(
      key: key,
      value: value,
    );

    return await db.update(
      DatabaseTables.appSettings,
      setting.toMap(),
      where: '${AppSettingsFields.key} = ?',
      whereArgs: [key],
    );
  }

  Future<DateTime?> getAppExpiryDate() async {
    final setting = await getSetting(SettingsKeys.appExpiryDate);
    if (setting != null) {
      return DateTime.parse(setting.value);
    }
    return null;
  }

  Future<bool> isAppExpired() async {
    final expiryDate = await getAppExpiryDate();
    if (expiryDate != null) {
      return DateTime.now().isAfter(expiryDate);
    }
    return false;
  }

  Future<void> closeDatabase() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
    }
  }
}
