import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../services/auth_service.dart';
import '../../services/network_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  DateTime? _appExpiryDate;
  bool _isLoading = true;
  Map<String, dynamic>? _networkInfo;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final networkService = Provider.of<NetworkService>(context, listen: false);
      
      final expiryDate = await authService.getAppExpiryDate();
      final networkInfo = await networkService.getNetworkInfo();
      
      setState(() {
        _appExpiryDate = expiryDate;
        _networkInfo = networkInfo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorDialog('Failed to load settings: $e');
    }
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  Future<void> _updateExpiryDate() async {
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: _appExpiryDate ?? DateTime.now().add(const Duration(days: 365)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 3650)), // 10 years
      helpText: 'Select App Expiry Date',
    );

    if (selectedDate != null) {
      try {
        final authService = Provider.of<AuthService>(context, listen: false);
        await authService.updateAppExpiry(selectedDate);
        
        setState(() {
          _appExpiryDate = selectedDate;
        });
        
        _showSuccessSnackBar('App expiry date updated successfully');
      } catch (e) {
        _showErrorDialog('Failed to update expiry date: $e');
      }
    }
  }

  Future<void> _refreshNetworkStatus() async {
    final networkService = Provider.of<NetworkService>(context, listen: false);
    await networkService.checkConnectivity();
    
    final networkInfo = await networkService.getNetworkInfo();
    setState(() {
      _networkInfo = networkInfo;
    });
    
    _showSuccessSnackBar('Network status refreshed');
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Settings',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey.shade800,
            ),
          ),
          const SizedBox(height: 24),
          
          if (_isLoading)
            const Center(child: CircularProgressIndicator())
          else ...[
            // Network Status Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.wifi,
                          color: Colors.blue.shade600,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Network Connection Status',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: _refreshNetworkStatus,
                          icon: const Icon(Icons.refresh),
                          tooltip: 'Refresh Network Status',
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    if (_networkInfo != null) ...[
                      _buildInfoRow(
                        'Status',
                        _networkInfo!['status'] as String,
                        icon: _networkInfo!['isConnected'] as bool
                            ? Icons.check_circle
                            : Icons.error,
                        iconColor: _networkInfo!['isConnected'] as bool
                            ? Colors.green
                            : Colors.red,
                      ),
                      _buildInfoRow(
                        'Connection Type',
                        (_networkInfo!['connectionType'] as String)
                            .split('.')
                            .last
                            .toUpperCase(),
                      ),
                      _buildInfoRow(
                        'Last Check',
                        _networkInfo!['lastCheckFormatted'] as String,
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // App Expiry Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          color: Colors.orange.shade600,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'App Expiry Management',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    if (_appExpiryDate != null) ...[
                      _buildInfoRow(
                        'Current Expiry Date',
                        DateFormat('dd/MM/yyyy').format(_appExpiryDate!),
                        icon: DateTime.now().isAfter(_appExpiryDate!)
                            ? Icons.warning
                            : Icons.check_circle,
                        iconColor: DateTime.now().isAfter(_appExpiryDate!)
                            ? Colors.red
                            : Colors.green,
                      ),
                      
                      if (DateTime.now().isAfter(_appExpiryDate!))
                        Container(
                          margin: const EdgeInsets.only(top: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade50,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.shade200),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.warning, color: Colors.red.shade600),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  'App has expired! Only admin users can login.',
                                  style: TextStyle(
                                    color: Colors.red.shade700,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      
                      const SizedBox(height: 16),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: _updateExpiryDate,
                          icon: const Icon(Icons.edit_calendar),
                          label: const Text('Update Expiry Date'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange.shade600,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            
            // System Information Card
            Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.green.shade600,
                          size: 24,
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'System Information',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    _buildInfoRow('App Version', '1.0.0'),
                    _buildInfoRow('Database', 'SQLite (Encrypted)'),
                    _buildInfoRow('Platform', 'Flutter'),
                    _buildInfoRow('Build Date', DateTime.now().toString().split(' ')[0]),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    IconData? icon,
    Color? iconColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        children: [
          SizedBox(
            width: 140,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 14,
              ),
            ),
          ),
          if (icon != null) ...[
            Icon(
              icon,
              size: 16,
              color: iconColor ?? Colors.grey.shade600,
            ),
            const SizedBox(width: 8),
          ],
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
