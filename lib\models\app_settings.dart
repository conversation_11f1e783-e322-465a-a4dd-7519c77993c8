import '../utils/constants.dart';

class AppSettings {
  final int? id;
  final String key;
  final String value;
  final DateTime updatedAt;

  AppSettings({
    this.id,
    required this.key,
    required this.value,
    DateTime? updatedAt,
  }) : updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      AppSettingsFields.id: id,
      AppSettingsFields.key: key,
      AppSettingsFields.value: value,
      AppSettingsFields.updatedAt: updatedAt.toIso8601String(),
    };
  }

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      id: map[AppSettingsFields.id]?.toInt(),
      key: map[AppSettingsFields.key] ?? '',
      value: map[AppSettingsFields.value] ?? '',
      updatedAt: DateTime.parse(map[AppSettingsFields.updatedAt]),
    );
  }

  AppSettings copyWith({
    int? id,
    String? key,
    String? value,
    DateTime? updatedAt,
  }) {
    return AppSettings(
      id: id ?? this.id,
      key: key ?? this.key,
      value: value ?? this.value,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'AppSettings{id: $id, key: $key, value: $value}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is AppSettings &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          key == other.key;

  @override
  int get hashCode => id.hashCode ^ key.hashCode;
}
