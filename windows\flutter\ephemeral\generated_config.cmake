# Generated code do not commit.
file(TO_CMAKE_PATH "E:\\Dev\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "D:\\GitHub\\flutter\\student_demo" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=E:\\Dev\\flutter"
  "PROJECT_DIR=D:\\GitHub\\flutter\\student_demo"
  "FLUTTER_ROOT=E:\\Dev\\flutter"
  "FLUTTER_EPHEMERAL_DIR=D:\\GitHub\\flutter\\student_demo\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=D:\\GitHub\\flutter\\student_demo"
  "FLUTTER_TARGET=D:\\GitHub\\flutter\\student_demo\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=D:\\GitHub\\flutter\\student_demo\\.dart_tool\\package_config.json"
)
